/* 导入Quicksand字体 */
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap');

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Quicksand', sans-serif;
    background-color: #383838; /* rgb(56,56,56) */
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
}

/* 游戏主容器 - 全屏 */
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background-color: #383838;
    display: flex;
}

/* 地图区域 */
.map-area {
    position: relative;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 0;
}

.map-background {
    position: absolute;
    background-color: #383838;
    z-index: 1;
}

.map-grid {
    position: relative;
    display: grid;
    gap: 2px;
    z-index: 2;
    background-color: #383838;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 4;
    pointer-events: none;
}

/* 地图格子样式 */
.map-cell {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    line-height: 1;
}

/* 地形类型样式 */
.cell-land {
    background-color: #dcdcdc;
}

.cell-mountain {
    background-image: url('../img/Mountain.png');
    background-size: cover;
    background-position: center;
}

.cell-city {
    background-image: url('../img/City.png');
    background-size: cover;
    background-position: center;
}

.cell-general {
    background-image: url('../img/General.png');
    background-size: cover;
    background-position: center;
}

.cell-swamp {
    background-image: url('../img/Swamp.png');
    background-size: cover;
    background-position: center;
}

.cell-obstacle {
    background-image: url('../img/Obstacle.png');
    background-size: cover;
    background-position: center;
}

/* 玩家颜色 */
.cell-player-0 { background-color: #DCDCDC; }
.cell-player-1 { background-color: #0288D1; }
.cell-player-2 { background-color: #E57373; }
.cell-player-3 { background-color: #9575CD; }
.cell-player-4 { background-color: #FF9800; }
.cell-player-5 { background-color: #FFCA28; }
.cell-player-6 { background-color: #8D6E63; }
.cell-player-7 { background-color: #66BB6A; }
.cell-player-8 { background-color: #4DB6AC; }
.cell-player-9 { background-color: #3949AB; }
.cell-player-10 { background-color: #D32F2F; }
.cell-player-11 { background-color: #5E35B1; }
.cell-player-12 { background-color: #FF5722; }
.cell-player-13 { background-color: #AFB42B; }
.cell-player-14 { background-color: #5D4037; }
.cell-player-15 { background-color: #2E7D32; }
.cell-player-16 { background-color: #00695C; }
.cell-player-17 { background-color: #FFFFFF; }

/* 箭头样式 */
.arrow {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: 3;
    pointer-events: none;
}

.arrow-up { background-image: url('../img/Arrow-Up.png'); }
.arrow-down { background-image: url('../img/Arrow-Down.png'); }
.arrow-left { background-image: url('../img/Arrow-Left.png'); }
.arrow-right { background-image: url('../img/Arrow-Right.png'); }

/* 焦点指示器 */
.focus-indicator {
    position: absolute;
    background-image: url('../img/Focus.png');
    background-size: cover;
    background-position: center;
    z-index: 5;
    pointer-events: none;
    transition: all 0.1s ease;
}

/* 阴影遮罩 */
.shadow-mask {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 3;
    pointer-events: none;
}

/* 右侧面板 */
.right-panel {
    width: 300px;
    height: 100vh;
    background-color: #383838;
    display: flex;
    flex-direction: column;
    border-left: 2px solid #555;
}

/* 排行榜区域 */
.leaderboard {
    flex: 0 0 auto;
    padding: 10px;
    border-bottom: 2px solid #555;
}

.round-info {
    font-family: 'Quicksand', sans-serif;
    font-weight: 700;
    font-size: 16px;
    color: #e1f5fe;
    background-color: rgba(255, 255, 255, 0.376);
    padding: 8px;
    text-align: center;
    margin-bottom: 10px;
    border-radius: 4px;
}

.board-header {
    display: flex;
    background-color: rgba(255, 255, 255, 0.376);
    border-radius: 4px;
    margin-bottom: 5px;
}

.board-col {
    flex: 1;
    padding: 6px 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    font-size: 14px;
    color: #e1f5fe;
    text-align: center;
}

.board-content {
    max-height: 400px;
    overflow-y: auto;
}

.board-row {
    display: flex;
    margin-bottom: 2px;
    border-radius: 4px;
    overflow: hidden;
}

.board-cell {
    flex: 1;
    padding: 6px 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
}

.board-cell.team-header {
    background-color: rgba(255, 255, 255, 0.376);
    font-weight: 600;
    color: #e1f5fe;
}

/* 聊天区域 */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    min-height: 0;
}

.chat-messages {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px dashed #666;
    border-radius: 4px;
    padding: 8px;
    overflow-y: auto;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    line-height: 1.4;
    margin-bottom: 10px;
}

.chat-input-container {
    flex: 0 0 auto;
}

.chat-input {
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px dashed #666;
    border-radius: 4px;
    padding: 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    outline: none;
}

.chat-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.chat-input:focus {
    border-color: #448aff;
    box-shadow: 0 0 4px rgba(68, 138, 255, 0.3);
}

/* 游戏控制面板 */
.game-controls {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    gap: 10px;
}

.control-btn {
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 14px;
    background-color: rgba(68, 138, 255, 0.8);
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background-color: rgba(68, 138, 255, 0.9);
}

.control-btn:active {
    background-color: rgba(68, 138, 255, 0.7);
}

/* 模态框样式 */
.end-game-modal,
.surrender-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: #242424;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.modal-header {
    margin-bottom: 20px;
}

.modal-icon {
    width: 80px;
    height: 64px;
    object-fit: contain;
}

.modal-body {
    margin-bottom: 30px;
}

.modal-title {
    font-family: 'Quicksand', sans-serif;
    font-weight: 700;
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 15px;
}

.modal-message {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

.modal-footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-btn {
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 16px;
    background-color: rgba(68, 138, 255, 0.8);
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-btn:hover {
    background-color: rgba(68, 138, 255, 0.9);
}

.modal-btn:active {
    background-color: rgba(68, 138, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .right-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .game-container {
        flex-direction: column;
    }
    
    .right-panel {
        width: 100%;
        height: 40vh;
        flex-direction: row;
    }
    
    .leaderboard {
        flex: 1;
        border-right: 2px solid #555;
        border-bottom: none;
    }
    
    .chat-area {
        flex: 1;
    }
}
