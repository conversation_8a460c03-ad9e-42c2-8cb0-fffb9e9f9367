<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generals.OI - Game</title>
    <link rel="stylesheet" href="css/game.css">
    <link rel="icon" href="img/Icon-Blue.png" type="image/png">
</head>
<body>
    <!-- 游戏主容器 - 全屏显示 -->
    <div class="game-container" id="game-container">
        <!-- 地图区域 -->
        <div class="map-area" id="map-area">
            <div class="map-background" id="map-background"></div>
            <div class="map-grid" id="map-grid">
                <!-- 动态生成地图格子 -->
            </div>
            <div class="map-overlay" id="map-overlay">
                <!-- 箭头、焦点等覆盖层 -->
            </div>
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel" id="right-panel">
            <!-- 排行榜区域 -->
            <div class="leaderboard" id="leaderboard">
                <div class="round-info" id="round-info">Round: 0</div>
                <div class="board-header">
                    <div class="board-col">Name</div>
                    <div class="board-col">Army</div>
                    <div class="board-col">Land</div>
                </div>
                <div class="board-content" id="board-content">
                    <!-- 动态生成排行榜内容 -->
                </div>
            </div>

            <!-- 聊天区域 -->
            <div class="chat-area" id="chat-area">
                <div class="chat-messages" id="chat-messages">
                    <!-- 聊天消息 -->
                </div>
                <div class="chat-input-container">
                    <input type="text" id="chat-input" class="chat-input" placeholder="Type message..." maxlength="100">
                </div>
            </div>
        </div>

        <!-- 焦点指示器 -->
        <div class="focus-indicator" id="focus-indicator"></div>

        <!-- 阴影遮罩（用于雾战） -->
        <div class="shadow-mask" id="shadow-mask"></div>

        <!-- 游戏控制面板（隐藏，通过键盘控制） -->
        <div class="game-controls" id="game-controls" style="display: none;">
            <button class="control-btn" id="surrender-btn">Surrender</button>
            <button class="control-btn" id="spectate-btn">Spectate</button>
        </div>
    </div>

    <!-- 结束游戏弹窗 -->
    <div class="end-game-modal" id="end-game-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <img src="img/General-Blue.png" alt="General" class="modal-icon">
            </div>
            <div class="modal-body">
                <h2 class="modal-title" id="modal-title">Game Over</h2>
                <p class="modal-message" id="modal-message">The game has ended.</p>
            </div>
            <div class="modal-footer">
                <button class="modal-btn" id="watch-btn">Watch</button>
                <button class="modal-btn" id="exit-btn">Exit</button>
            </div>
        </div>
    </div>

    <!-- 投降确认弹窗 -->
    <div class="surrender-modal" id="surrender-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-body">
                <h3 class="modal-title">Confirm Surrender</h3>
                <p class="modal-message">Are you sure you want to surrender?</p>
            </div>
            <div class="modal-footer">
                <button class="modal-btn" id="confirm-surrender-btn">Yes</button>
                <button class="modal-btn" id="cancel-surrender-btn">No</button>
            </div>
        </div>
    </div>

    <script src="js/websocket-client.js"></script>
    <script src="js/game-engine.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
