/**
 * 房间管理器 - 处理房间内的所有交互
 */
class RoomManager {
    constructor() {
        this.wsClient = window.wsClient || new WebSocketClient();
        this.roomId = this.getRoomIdFromUrl();
        this.playerId = null;
        this.currentPlayer = null;
        this.players = [];
        this.roomSettings = {};
        this.isHost = false;
        this.isReady = false;
        
        // DOM元素
        this.roomIdElement = null;
        this.roomStatusElement = null;
        this.playersListElement = null;
        this.teamButtonsElement = null;
        this.readyBtnElement = null;
        this.readyCountElement = null;
        this.totalCountElement = null;
        this.chatMessages = null;
        this.chatInput = null;
        
        this.initializeDOM();
        this.bindEvents();
        this.initializeWebSocket();
    }

    // 从URL获取房间ID
    getRoomIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/room\/(\d{4})/);
        return match ? match[1] : null;
    }

    // 初始化DOM元素
    initializeDOM() {
        this.roomIdElement = document.getElementById('room-id');
        this.roomStatusElement = document.getElementById('room-status');
        this.playersListElement = document.getElementById('players-list');
        this.teamButtonsElement = document.getElementById('team-buttons');
        this.readyBtnElement = document.getElementById('ready-btn');
        this.readyCountElement = document.getElementById('ready-count');
        this.totalCountElement = document.getElementById('total-count');
        this.chatMessages = document.getElementById('chat-messages');
        this.chatInput = document.getElementById('chat-input');
        
        // 设置房间ID
        if (this.roomId && this.roomIdElement) {
            this.roomIdElement.textContent = this.roomId;
        }
        
        // 生成队伍按钮
        this.generateTeamButtons();
    }

    // 绑定事件
    bindEvents() {
        // 离开房间
        document.getElementById('leave-btn')?.addEventListener('click', () => {
            this.leaveRoom();
        });
        
        // 准备按钮
        this.readyBtnElement?.addEventListener('click', () => {
            this.toggleReady();
        });
        
        // 应用设置
        document.getElementById('apply-settings-btn')?.addEventListener('click', () => {
            this.applySettings();
        });
        
        // 聊天
        this.chatInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });
        
        document.getElementById('send-btn')?.addEventListener('click', () => {
            this.sendChatMessage();
        });
        
        // 开始游戏模态框
        document.getElementById('start-game-btn')?.addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('cancel-start-btn')?.addEventListener('click', () => {
            document.getElementById('start-game-modal').style.display = 'none';
        });
        
        // WebSocket事件
        this.wsClient.on('ROOM_JOINED', (data) => this.handleRoomJoined(data));
        this.wsClient.on('room-update', (data) => this.handleRoomUpdate(data));
        this.wsClient.on('player-joined', (data) => this.handlePlayerJoined(data));
        this.wsClient.on('player-left', (data) => this.handlePlayerLeft(data));
        this.wsClient.on('player-ready', (data) => this.handlePlayerReady(data));
        this.wsClient.on('settings-updated', (data) => this.handleSettingsUpdated(data));
        this.wsClient.on('game-starting', (data) => this.handleGameStarting(data));
        this.wsClient.on('chat-message', (data) => this.handleChatMessage(data));
        this.wsClient.on('ERROR', (data) => this.handleError(data));
    }

    // 生成队伍按钮
    generateTeamButtons() {
        if (!this.teamButtonsElement) return;
        
        this.teamButtonsElement.innerHTML = '';
        
        // 创建队伍按钮（1-8队）
        for (let i = 1; i <= 8; i++) {
            const btn = document.createElement('button');
            btn.className = 'team-btn';
            btn.textContent = `Team ${i}`;
            btn.dataset.team = i;
            btn.addEventListener('click', () => this.selectTeam(i));
            this.teamButtonsElement.appendChild(btn);
        }
        
        // 默认选择队伍1
        this.selectTeam(1);
    }

    // 选择队伍
    selectTeam(teamId) {
        // 更新UI
        const teamBtns = this.teamButtonsElement.querySelectorAll('.team-btn');
        teamBtns.forEach(btn => {
            btn.classList.toggle('selected', parseInt(btn.dataset.team) === teamId);
        });
        
        // 发送队伍选择消息
        this.wsClient.send({
            type: 'SELECT_TEAM',
            payload: { roomId: this.roomId, playerId: this.playerId, teamId: teamId }
        });
    }

    // 初始化WebSocket连接
    initializeWebSocket() {
        if (this.wsClient.status === 'disconnected') {
            this.wsClient.connect();
        }

        // 等待连接建立后加入房间
        this.wsClient.on('connected', () => {
            this.connectToRoom();
        });

        // 如果已经连接，直接加入房间
        if (this.wsClient.status === 'connected') {
            this.connectToRoom();
        }
    }

    // 连接到房间
    connectToRoom() {
        if (!this.roomId) {
            this.showError('Invalid room ID');
            return;
        }

        // 从localStorage获取玩家信息
        const nickname = localStorage.getItem('nickname') || 'Anonymous';

        // 加入房间
        this.wsClient.send({
            type: 'JOIN_ROOM',
            payload: { roomId: this.roomId, nickname: nickname }
        });

        this.addChatMessage('System', `Joining room ${this.roomId}...`);
    }

    // 切换准备状态
    toggleReady() {
        this.isReady = !this.isReady;
        
        // 更新按钮显示
        this.readyBtnElement.textContent = this.isReady ? 'Not Ready' : 'Ready';
        this.readyBtnElement.classList.toggle('not-ready', this.isReady);
        
        // 发送准备状态
        this.wsClient.send({
            type: 'PLAYER_READY',
            payload: { roomId: this.roomId, playerId: this.playerId, ready: this.isReady }
        });
        
        this.addChatMessage('System', this.isReady ? 'You are ready!' : 'You are not ready.');
    }

    // 应用设置
    applySettings() {
        if (!this.isHost) {
            this.addChatMessage('System', 'Only the host can change settings.');
            return;
        }
        
        const settings = {
            gameMode: parseInt(document.getElementById('game-mode').value),
            gameSpeed: parseFloat(document.getElementById('game-speed').value),
            maxPlayers: parseInt(document.getElementById('max-players').value),
            map: document.getElementById('map-select').value
        };
        
        this.wsClient.send({
            type: 'UPDATE_SETTINGS',
            payload: { roomId: this.roomId, playerId: this.playerId, settings: settings }
        });
        
        this.addChatMessage('System', 'Settings updated.');
    }

    // 开始游戏
    startGame() {
        if (!this.isHost) {
            this.addChatMessage('System', 'Only the host can start the game.');
            return;
        }
        
        this.wsClient.send({
            type: 'START_GAME',
            payload: { roomId: this.roomId, playerId: this.playerId }
        });
        
        document.getElementById('start-game-modal').style.display = 'none';
    }

    // 离开房间
    leaveRoom() {
        this.wsClient.send({
            type: 'LEAVE_ROOM',
            payload: { roomId: this.roomId, playerId: this.playerId }
        });

        // 返回大厅
        window.location.href = '/lobby';
    }

    // 发送聊天消息
    sendChatMessage() {
        const message = this.chatInput.value.trim();
        if (message) {
            this.wsClient.send({
                type: 'CHAT_MESSAGE',
                payload: { roomId: this.roomId, message: message }
            });
            this.chatInput.value = '';
        }
    }

    // 添加聊天消息
    addChatMessage(sender, message) {
        const messageElement = document.createElement('div');
        messageElement.innerHTML = `<strong>${sender}:</strong> ${message}`;
        this.chatMessages.appendChild(messageElement);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    // 更新玩家列表显示
    updatePlayersDisplay() {
        if (!this.playersListElement) return;
        
        this.playersListElement.innerHTML = '';
        
        let readyCount = 0;
        
        this.players.forEach(player => {
            const playerElement = document.createElement('div');
            playerElement.className = 'player-item';
            
            if (player.ready) {
                playerElement.classList.add('ready');
                readyCount++;
            }
            
            if (player.isHost) {
                playerElement.classList.add('host');
            }
            
            playerElement.innerHTML = `
                <div class="player-info">
                    <div class="player-name">${player.nickname}</div>
                    <div class="player-team">Team ${player.teamId || 1}</div>
                </div>
                <div class="player-status ${player.ready ? 'ready' : ''} ${player.isHost ? 'host' : ''}">
                    ${player.isHost ? 'Host' : (player.ready ? 'Ready' : 'Not Ready')}
                </div>
            `;
            
            this.playersListElement.appendChild(playerElement);
        });
        
        // 更新准备状态显示
        if (this.readyCountElement && this.totalCountElement) {
            this.readyCountElement.textContent = readyCount;
            this.totalCountElement.textContent = this.players.length;
        }
        
        // 检查是否所有玩家都准备好了
        if (this.isHost && this.players.length >= 2 && readyCount === this.players.length) {
            document.getElementById('start-game-modal').style.display = 'flex';
        }
    }

    // 处理房间加入成功
    handleRoomJoined(data) {
        this.playerId = data.playerId;
        this.players = data.room.players || [];
        this.roomSettings = data.room.settings || {};

        // 找到当前玩家
        this.currentPlayer = this.players.find(p => p.id === this.playerId);
        if (this.currentPlayer) {
            this.isHost = this.currentPlayer.isHost;
        }

        this.updatePlayersDisplay();
        this.updateSettingsDisplay();
        this.addChatMessage('System', `Successfully joined room ${this.roomId}`);
    }

    // 处理房间更新
    handleRoomUpdate(data) {
        this.players = data.players || [];
        this.roomSettings = data.settings || {};

        // 检查当前玩家是否为房主
        const currentPlayer = this.players.find(p => p.id === this.playerId);
        if (currentPlayer) {
            this.isHost = currentPlayer.isHost;
            this.currentPlayer = currentPlayer;
        }

        this.updatePlayersDisplay();
        this.updateSettingsDisplay();
    }

    // 处理玩家加入
    handlePlayerJoined(data) {
        this.addChatMessage('System', `${data.nickname} joined the room.`);
        // 房间更新会通过room-update事件处理
    }

    // 处理玩家离开
    handlePlayerLeft(data) {
        this.addChatMessage('System', `${data.nickname} left the room.`);
        // 房间更新会通过room-update事件处理
    }

    // 处理玩家准备状态
    handlePlayerReady(data) {
        const { nickname, ready } = data;
        this.addChatMessage('System', `${nickname} is ${ready ? 'ready' : 'not ready'}.`);
        // 房间更新会通过room-update事件处理
    }

    // 处理设置更新
    handleSettingsUpdated(data) {
        this.roomSettings = data.settings;
        this.updateSettingsDisplay();
        this.addChatMessage('System', 'Room settings updated.');
    }

    // 处理游戏开始
    handleGameStarting(data) {
        this.addChatMessage('System', 'Game is starting...');
        
        // 跳转到游戏页面
        setTimeout(() => {
            window.location.href = `/game/${this.roomId}`;
        }, 2000);
    }

    // 处理聊天消息
    handleChatMessage(data) {
        this.addChatMessage(data.sender, data.message);
    }

    // 更新设置显示
    updateSettingsDisplay() {
        if (this.roomSettings.gameMode !== undefined) {
            document.getElementById('game-mode').value = this.roomSettings.gameMode;
        }
        if (this.roomSettings.gameSpeed !== undefined) {
            document.getElementById('game-speed').value = this.roomSettings.gameSpeed;
        }
        if (this.roomSettings.maxPlayers !== undefined) {
            document.getElementById('max-players').value = this.roomSettings.maxPlayers;
        }
        if (this.roomSettings.map !== undefined) {
            document.getElementById('map-select').value = this.roomSettings.map;
        }
    }

    // 处理错误
    handleError(data) {
        this.addChatMessage('Error', data.message);
    }

    // 显示错误
    showError(message) {
        this.addChatMessage('Error', message);
    }
}

// 页面加载完成后初始化房间管理器
document.addEventListener('DOMContentLoaded', () => {
    window.roomManager = new RoomManager();
});
