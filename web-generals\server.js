const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const server = http.createServer(app);

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public'), {
    setHeaders: (res, path) => {
        if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css');
        } else if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
        } else if (path.endsWith('.png')) {
            res.setHeader('Content-Type', 'image/png');
        }
    }
}));
app.use(express.json());

// 房间管理
class RoomManager {
    constructor() {
        this.rooms = new Map(); // roomId -> Room
        this.qtServers = new Map(); // roomId -> Qt Server connection info
        this.playerSockets = new Map(); // playerId -> WebSocket
    }

    generateRoomId() {
        let roomId;
        do {
            roomId = Math.floor(1000 + Math.random() * 9000).toString();
        } while (this.rooms.has(roomId));
        return roomId;
    }

    createRoom(settings = {}) {
        const roomId = this.generateRoomId();
        const room = {
            id: roomId,
            players: [],
            status: 'waiting', // waiting, ready, playing, finished
            settings: {
                gameMode: settings.gameMode || 0,
                gameSpeed: settings.gameSpeed || 1.0,
                maxPlayers: settings.maxPlayers || 8,
                map: settings.map || 'random'
            },
            createdAt: new Date(),
            hostId: null
        };
        this.rooms.set(roomId, room);
        return room;
    }

    getRoom(roomId) {
        return this.rooms.get(roomId);
    }

    getRoomList() {
        return Array.from(this.rooms.values()).map(room => ({
            id: room.id,
            playerCount: room.players.length,
            maxPlayers: room.settings.maxPlayers,
            status: room.status
        }));
    }

    joinRoom(roomId, playerInfo) {
        const room = this.rooms.get(roomId);
        if (!room) return null;
        if (room.players.length >= room.settings.maxPlayers) return null;

        // 设置玩家属性
        playerInfo.ready = false;
        playerInfo.teamId = 1; // 默认队伍1
        playerInfo.joinedAt = new Date();

        // 如果是第一个玩家，设为房主
        if (room.players.length === 0) {
            playerInfo.isHost = true;
            room.hostId = playerInfo.id;
        } else {
            playerInfo.isHost = false;
        }

        room.players.push(playerInfo);
        this.playerSockets.set(playerInfo.id, playerInfo.ws);

        return room;
    }

    leaveRoom(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room) return null;

        const playerIndex = room.players.findIndex(p => p.id === playerId);
        if (playerIndex === -1) return null;

        const player = room.players[playerIndex];
        room.players.splice(playerIndex, 1);
        this.playerSockets.delete(playerId);

        // 如果离开的是房主，转移房主权限
        if (player.isHost && room.players.length > 0) {
            room.players[0].isHost = true;
            room.hostId = room.players[0].id;
        }

        // 如果房间空了，删除房间
        if (room.players.length === 0) {
            this.rooms.delete(roomId);
        }

        return { room, leftPlayer: player };
    }

    setPlayerReady(roomId, playerId, ready) {
        const room = this.rooms.get(roomId);
        if (!room) return null;

        const player = room.players.find(p => p.id === playerId);
        if (!player) return null;

        player.ready = ready;
        return { room, player };
    }

    selectTeam(roomId, playerId, teamId) {
        const room = this.rooms.get(roomId);
        if (!room) return null;

        const player = room.players.find(p => p.id === playerId);
        if (!player) return null;

        player.teamId = teamId;
        return { room, player };
    }

    updateSettings(roomId, playerId, settings) {
        const room = this.rooms.get(roomId);
        if (!room) return null;

        const player = room.players.find(p => p.id === playerId);
        if (!player || !player.isHost) return null;

        room.settings = { ...room.settings, ...settings };
        return room;
    }

    canStartGame(roomId) {
        const room = this.rooms.get(roomId);
        if (!room) return false;

        // 至少2个玩家，所有玩家都准备好
        return room.players.length >= 2 && room.players.every(p => p.ready);
    }

    startGame(roomId, hostId) {
        const room = this.rooms.get(roomId);
        if (!room) return null;

        const host = room.players.find(p => p.id === hostId);
        if (!host || !host.isHost) return null;

        if (!this.canStartGame(roomId)) return null;

        room.status = 'playing';
        return room;
    }

    broadcastToRoom(roomId, message, excludePlayerId = null) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        room.players.forEach(player => {
            if (player.id !== excludePlayerId) {
                const ws = this.playerSockets.get(player.id);
                if (ws && ws.readyState === 1) { // WebSocket.OPEN
                    ws.send(JSON.stringify(message));
                }
            }
        });
    }
}

const roomManager = new RoomManager();

// WebSocket服务器
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
    console.log('New WebSocket connection established');
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleWebSocketMessage(ws, data);
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    });

    ws.on('close', () => {
        console.log('WebSocket connection closed');
    });
});

function handleWebSocketMessage(ws, data) {
    const { type, payload } = data;
    console.log('Received WebSocket message:', type, payload);

    switch (type) {
        case 'CREATE_ROOM':
            const room = roomManager.createRoom(payload.settings);
            ws.send(JSON.stringify({
                type: 'ROOM_CREATED',
                payload: { roomId: room.id }
            }));
            break;

        case 'JOIN_ROOM':
            const playerId = uuidv4();

            // 如果房间不存在，先创建房间
            let targetRoom = roomManager.getRoom(payload.roomId);
            if (!targetRoom) {
                targetRoom = roomManager.createRoom();
                targetRoom.id = payload.roomId; // 使用指定的房间ID
                roomManager.rooms.set(payload.roomId, targetRoom);
            }

            const joinedRoom = roomManager.joinRoom(payload.roomId, {
                id: playerId,
                nickname: payload.nickname,
                ws: ws
            });

            if (joinedRoom) {
                // 发送加入成功消息给当前玩家
                ws.send(JSON.stringify({
                    type: 'ROOM_JOINED',
                    payload: {
                        room: joinedRoom,
                        playerId: playerId
                    }
                }));

                // 广播玩家加入消息给房间内其他玩家
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'player-joined',
                    payload: { nickname: payload.nickname }
                }, playerId);

                // 广播房间更新给所有玩家
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'room-update',
                    payload: {
                        players: joinedRoom.players.map(p => ({
                            id: p.id,
                            nickname: p.nickname,
                            ready: p.ready,
                            teamId: p.teamId,
                            isHost: p.isHost
                        })),
                        settings: joinedRoom.settings
                    }
                });
            } else {
                ws.send(JSON.stringify({
                    type: 'ERROR',
                    payload: { message: 'Room not found or full' }
                }));
            }
            break;

        case 'LEAVE_ROOM':
            const leaveResult = roomManager.leaveRoom(payload.roomId, payload.playerId);
            if (leaveResult) {
                // 广播玩家离开消息
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'player-left',
                    payload: { nickname: leaveResult.leftPlayer.nickname }
                });

                // 广播房间更新
                if (leaveResult.room.players.length > 0) {
                    roomManager.broadcastToRoom(payload.roomId, {
                        type: 'room-update',
                        payload: {
                            players: leaveResult.room.players.map(p => ({
                                id: p.id,
                                nickname: p.nickname,
                                ready: p.ready,
                                teamId: p.teamId,
                                isHost: p.isHost
                            })),
                            settings: leaveResult.room.settings
                        }
                    });
                }
            }
            break;

        case 'PLAYER_READY':
            const readyResult = roomManager.setPlayerReady(payload.roomId, payload.playerId, payload.ready);
            if (readyResult) {
                // 广播准备状态更新
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'player-ready',
                    payload: {
                        nickname: readyResult.player.nickname,
                        ready: payload.ready
                    }
                });

                // 广播房间更新
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'room-update',
                    payload: {
                        players: readyResult.room.players.map(p => ({
                            id: p.id,
                            nickname: p.nickname,
                            ready: p.ready,
                            teamId: p.teamId,
                            isHost: p.isHost
                        })),
                        settings: readyResult.room.settings
                    }
                });
            }
            break;

        case 'SELECT_TEAM':
            const teamResult = roomManager.selectTeam(payload.roomId, payload.playerId, payload.teamId);
            if (teamResult) {
                // 广播房间更新
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'room-update',
                    payload: {
                        players: teamResult.room.players.map(p => ({
                            id: p.id,
                            nickname: p.nickname,
                            ready: p.ready,
                            teamId: p.teamId,
                            isHost: p.isHost
                        })),
                        settings: teamResult.room.settings
                    }
                });
            }
            break;

        case 'UPDATE_SETTINGS':
            const updatedRoom = roomManager.updateSettings(payload.roomId, payload.playerId, payload.settings);
            if (updatedRoom) {
                // 广播设置更新
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'settings-updated',
                    payload: { settings: updatedRoom.settings }
                });
            }
            break;

        case 'START_GAME':
            const gameRoom = roomManager.startGame(payload.roomId, payload.playerId);
            if (gameRoom) {
                // 广播游戏开始
                roomManager.broadcastToRoom(payload.roomId, {
                    type: 'game-starting',
                    payload: { roomId: payload.roomId }
                });
            }
            break;

        case 'CHAT_MESSAGE':
            const chatRoom = roomManager.getRoom(payload.roomId);
            if (chatRoom) {
                const player = chatRoom.players.find(p => p.ws === ws);
                if (player) {
                    // 广播聊天消息
                    roomManager.broadcastToRoom(payload.roomId, {
                        type: 'chat-message',
                        payload: {
                            sender: player.nickname,
                            message: payload.message
                        }
                    });
                }
            }
            break;

        case 'GET_ROOM_LIST':
            ws.send(JSON.stringify({
                type: 'ROOM_LIST',
                payload: { rooms: roomManager.getRoomList() }
            }));
            break;
    }
}

// HTTP路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'lobby.html'));
});

app.get('/lobby', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'lobby.html'));
});

app.get('/room/:roomId', (req, res) => {
    const roomId = req.params.roomId;

    // 验证房间ID格式
    if (!/^\d{4}$/.test(roomId)) {
        return res.status(400).send('Invalid room ID format');
    }

    // 允许访问不存在的房间（玩家加入时会自动创建）
    res.sendFile(path.join(__dirname, 'public', 'room.html'));
});

app.get('/game/:roomId', (req, res) => {
    const roomId = req.params.roomId;

    // 验证房间ID格式
    if (!/^\d{4}$/.test(roomId)) {
        return res.status(400).send('Invalid room ID format');
    }

    // 对于游戏页面，我们允许访问不存在的房间（可能是直接访问）
    // 房间验证将在WebSocket连接时进行
    res.sendFile(path.join(__dirname, 'public', 'game.html'));
});

// API路由
app.get('/api/rooms', (req, res) => {
    res.json({ rooms: roomManager.getRoomList() });
});

app.post('/api/rooms', (req, res) => {
    const room = roomManager.createRoom(req.body.settings);
    res.json({ room });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Web Generals server running on port ${PORT}`);
    console.log(`Lobby: http://localhost:${PORT}/lobby`);
});
