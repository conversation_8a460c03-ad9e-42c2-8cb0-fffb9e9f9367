const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const server = http.createServer(app);

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// 房间管理
class RoomManager {
    constructor() {
        this.rooms = new Map(); // roomId -> Room
        this.qtServers = new Map(); // roomId -> Qt Server connection info
    }

    generateRoomId() {
        let roomId;
        do {
            roomId = Math.floor(1000 + Math.random() * 9000).toString();
        } while (this.rooms.has(roomId));
        return roomId;
    }

    createRoom(settings = {}) {
        const roomId = this.generateRoomId();
        const room = {
            id: roomId,
            players: [],
            status: 'waiting', // waiting, playing, finished
            settings: {
                gameMode: settings.gameMode || 0,
                gameSpeed: settings.gameSpeed || 1.0,
                maxPlayers: settings.maxPlayers || 8
            },
            createdAt: new Date()
        };
        this.rooms.set(roomId, room);
        return room;
    }

    getRoom(roomId) {
        return this.rooms.get(roomId);
    }

    getRoomList() {
        return Array.from(this.rooms.values()).map(room => ({
            id: room.id,
            playerCount: room.players.length,
            maxPlayers: room.settings.maxPlayers,
            status: room.status
        }));
    }

    joinRoom(roomId, playerInfo) {
        const room = this.rooms.get(roomId);
        if (!room) return null;
        if (room.players.length >= room.settings.maxPlayers) return null;
        
        room.players.push(playerInfo);
        return room;
    }
}

const roomManager = new RoomManager();

// WebSocket服务器
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
    console.log('New WebSocket connection');
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleWebSocketMessage(ws, data);
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    });

    ws.on('close', () => {
        console.log('WebSocket connection closed');
    });
});

function handleWebSocketMessage(ws, data) {
    const { type, payload } = data;
    
    switch (type) {
        case 'CREATE_ROOM':
            const room = roomManager.createRoom(payload.settings);
            ws.send(JSON.stringify({
                type: 'ROOM_CREATED',
                payload: { roomId: room.id }
            }));
            break;
            
        case 'JOIN_ROOM':
            const joinedRoom = roomManager.joinRoom(payload.roomId, {
                id: uuidv4(),
                nickname: payload.nickname,
                ws: ws
            });
            if (joinedRoom) {
                ws.send(JSON.stringify({
                    type: 'ROOM_JOINED',
                    payload: { room: joinedRoom }
                }));
            } else {
                ws.send(JSON.stringify({
                    type: 'ERROR',
                    payload: { message: 'Room not found or full' }
                }));
            }
            break;
            
        case 'GET_ROOM_LIST':
            ws.send(JSON.stringify({
                type: 'ROOM_LIST',
                payload: { rooms: roomManager.getRoomList() }
            }));
            break;
    }
}

// HTTP路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'lobby.html'));
});

app.get('/lobby', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'lobby.html'));
});

app.get('/room/:roomId', (req, res) => {
    const roomId = req.params.roomId;
    const room = roomManager.getRoom(roomId);
    if (!room) {
        return res.status(404).send('Room not found');
    }
    res.sendFile(path.join(__dirname, 'public', 'room.html'));
});

app.get('/game/:roomId', (req, res) => {
    const roomId = req.params.roomId;

    // 验证房间ID格式
    if (!/^\d{4}$/.test(roomId)) {
        return res.status(400).send('Invalid room ID format');
    }

    // 对于游戏页面，我们允许访问不存在的房间（可能是直接访问）
    // 房间验证将在WebSocket连接时进行
    res.sendFile(path.join(__dirname, 'public', 'game.html'));
});

// API路由
app.get('/api/rooms', (req, res) => {
    res.json({ rooms: roomManager.getRoomList() });
});

app.post('/api/rooms', (req, res) => {
    const room = roomManager.createRoom(req.body.settings);
    res.json({ room });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`Web Generals server running on port ${PORT}`);
    console.log(`Lobby: http://localhost:${PORT}/lobby`);
});
