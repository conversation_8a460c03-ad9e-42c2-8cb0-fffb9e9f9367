/**
 * 游戏引擎 - 完全复制Qt GameWindow的逻辑
 */
class GameEngine {
    constructor() {
        // 游戏状态
        this.gameMap = null;
        this.previousMap = null;
        this.playersInfo = [];
        this.gameMode = 0;
        this.round = 0;
        this.gameEnded = false;
        this.surrendered = false;
        this.spectated = false;
        
        // 玩家信息
        this.playerId = -1;
        this.teamId = -1;
        this.nickname = '';
        
        // 地图尺寸
        this.mapWidth = 0;
        this.mapHeight = 0;
        this.unitSize = 30; // 默认格子大小
        this.minUnitSize = 22;
        
        // 焦点系统
        this.focus = { x: 1, y: 1, width: 0, height: 0 };
        this.visibilityMap = [];
        
        // 移动系统
        this.moveQueue = [];
        this.moved = false;
        
        // 字体大小映射
        this.fontSizeMap = [0.36, 0.28, 0.22, 0.18, 0.16, 0.13];
        
        // 颜色数组（与Qt完全一致）
        this.playerColors = [
            "#DCDCDC", "#0288D1", "#E57373", "#9575CD", "#FF9800",
            "#FFCA28", "#8D6E63", "#66BB6A", "#4DB6AC", "#3949AB",
            "#D32F2F", "#5E35B1", "#FF5722", "#AFB42B", "#5D4037",
            "#2E7D32", "#00695C", "#FFFFFF"
        ];
        
        // 地形类型
        this.CellType = {
            LAND: 0,
            GENERAL: 1,
            CITY: 2,
            MOUNTAIN: 3,
            SWAMP: 4
        };
        
        // 游戏模式
        this.GameMode = {
            CRYSTAL_CLEAR: 1,
            MISTY_VEIL: 2,
            SILENT_WAR: 4
        };
        
        // 方向数组
        this.directions = [
            { x: -1, y: 0 }, // 上
            { x: 1, y: 0 },  // 下
            { x: 0, y: -1 }, // 左
            { x: 0, y: 1 }   // 右
        ];
    }

    // 初始化游戏地图
    initializeMap(mapData, gameMode) {
        this.gameMode = gameMode;
        this.parseMapData(mapData);
        this.calculateMapSize();
        this.initializeFocus();
        this.updateVisibility();
    }

    // 解析地图数据
    parseMapData(mapData) {
        // 解析Qt传来的地图数据格式
        // 格式: [width, height, ...cellData]
        this.mapWidth = mapData[0];
        this.mapHeight = mapData[1];
        
        this.gameMap = [];
        this.previousMap = [];
        this.visibilityMap = [];
        
        for (let i = 0; i <= this.mapHeight; i++) {
            this.gameMap[i] = [];
            this.previousMap[i] = [];
            this.visibilityMap[i] = [];
            for (let j = 0; j <= this.mapWidth; j++) {
                this.gameMap[i][j] = {
                    type: this.CellType.LAND,
                    belonging: 0,
                    number: 0
                };
                this.previousMap[i][j] = { ...this.gameMap[i][j] };
                this.visibilityMap[i][j] = false;
            }
        }
        
        // 解析具体的地图数据
        let dataIndex = 2;
        for (let i = 1; i <= this.mapHeight; i++) {
            for (let j = 1; j <= this.mapWidth; j++) {
                if (dataIndex < mapData.length) {
                    const cellData = mapData[dataIndex++];
                    this.gameMap[i][j] = {
                        type: cellData.type || this.CellType.LAND,
                        belonging: cellData.belonging || 0,
                        number: cellData.number || 0
                    };
                }
            }
        }
    }

    // 计算地图显示尺寸
    calculateMapSize() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const rightPanelWidth = 300;
        
        const availableWidth = screenWidth - rightPanelWidth;
        const availableHeight = screenHeight;
        
        const unitSizeByWidth = Math.floor(availableWidth / this.mapWidth);
        const unitSizeByHeight = Math.floor(availableHeight / this.mapHeight);
        
        this.unitSize = Math.max(this.minUnitSize, Math.min(unitSizeByWidth, unitSizeByHeight));
    }

    // 初始化焦点系统
    initializeFocus() {
        this.focus = {
            x: 1,
            y: 1,
            width: this.mapWidth,
            height: this.mapHeight
        };
    }

    // 更新可见性（雾战）
    updateVisibility() {
        if (this.playerId === -1 || (this.gameMode & this.GameMode.CRYSTAL_CLEAR) || this.gameEnded) {
            // 观战者或透明模式，全部可见
            for (let i = 1; i <= this.mapHeight; i++) {
                for (let j = 1; j <= this.mapWidth; j++) {
                    this.visibilityMap[i][j] = true;
                }
            }
            return;
        }

        // 重置可见性
        for (let i = 1; i <= this.mapHeight; i++) {
            for (let j = 1; j <= this.mapWidth; j++) {
                this.visibilityMap[i][j] = false;
            }
        }

        // 计算可见区域（己方领土周围8格）
        for (let i = 1; i <= this.mapHeight; i++) {
            for (let j = 1; j <= this.mapWidth; j++) {
                const cell = this.gameMap[i][j];
                if (this.isOwnTerritory(cell)) {
                    // 标记周围9格为可见
                    for (let di = -1; di <= 1; di++) {
                        for (let dj = -1; dj <= 1; dj++) {
                            const ni = i + di;
                            const nj = j + dj;
                            if (this.isValidPosition(ni, nj)) {
                                this.visibilityMap[ni][nj] = true;
                            }
                        }
                    }
                }
            }
        }
    }

    // 检查是否为己方领土
    isOwnTerritory(cell) {
        if (!cell.belonging) return false;
        
        const playerInfo = this.playersInfo[cell.belonging];
        if (!playerInfo) return false;
        
        return playerInfo.teamId === this.teamId;
    }

    // 检查位置是否有效
    isValidPosition(x, y) {
        return x >= 1 && x <= this.mapHeight && y >= 1 && y <= this.mapWidth;
    }

    // 检查位置是否可见
    isPositionVisible(x, y) {
        if (!this.isValidPosition(x, y)) return false;
        return this.visibilityMap[x][y];
    }

    // 移动焦点
    moveFocus(direction) {
        const { x, y } = this.focus;
        let newX = x;
        let newY = y;
        
        switch (direction) {
            case 'up':
                newX = Math.max(1, x - 1);
                break;
            case 'down':
                newX = Math.min(this.mapHeight, x + 1);
                break;
            case 'left':
                newY = Math.max(1, y - 1);
                break;
            case 'right':
                newY = Math.min(this.mapWidth, y + 1);
                break;
        }
        
        this.focus.x = newX;
        this.focus.y = newY;
        
        return { x: newX, y: newY };
    }

    // 设置焦点位置
    setFocus(x, y) {
        if (this.isValidPosition(x, y)) {
            this.focus.x = x;
            this.focus.y = y;
            return true;
        }
        return false;
    }

    // 获取格子显示的数字字体大小级别
    getFontSizeLevel(number) {
        if (number < 1000) return 0;
        return Math.min(Math.floor(Math.log10(number)) - 2, this.fontSizeMap.length - 1);
    }

    // 更新地图差异
    updateMapDiff(diffData) {
        // 保存之前的地图状态
        this.previousMap = JSON.parse(JSON.stringify(this.gameMap));
        
        // 应用差异更新
        this.applyMapDiff(diffData);
        
        // 更新可见性
        this.updateVisibility();
        
        // 更新回合数
        if (diffData.round !== undefined) {
            this.round = diffData.round;
        }
    }

    // 应用地图差异
    applyMapDiff(diffData) {
        // 根据Qt的差异格式解析并应用更新
        // 这里需要根据实际的差异数据格式来实现
        if (diffData.cells) {
            diffData.cells.forEach(cellUpdate => {
                const { x, y, type, belonging, number } = cellUpdate;
                if (this.isValidPosition(x, y)) {
                    if (type !== undefined) this.gameMap[x][y].type = type;
                    if (belonging !== undefined) this.gameMap[x][y].belonging = belonging;
                    if (number !== undefined) this.gameMap[x][y].number = number;
                }
            });
        }
    }

    // 聚焦到将军位置
    focusGeneral() {
        for (let i = 1; i <= this.mapHeight; i++) {
            for (let j = 1; j <= this.mapWidth; j++) {
                const cell = this.gameMap[i][j];
                if (cell.type === this.CellType.GENERAL && this.isOwnTerritory(cell)) {
                    this.setFocus(i, j);
                    return { x: i, y: j };
                }
            }
        }
        return null;
    }

    // 获取排行榜数据
    getLeaderboardData() {
        // 这里需要根据实际的统计数据格式来实现
        return {
            round: this.round,
            teams: [],
            players: []
        };
    }

    // 检查游戏是否结束
    checkGameEnd() {
        return this.gameEnded;
    }

    // 设置玩家信息
    setPlayerInfo(playerId, teamId, nickname) {
        this.playerId = playerId;
        this.teamId = teamId;
        this.nickname = nickname;
    }

    // 设置玩家列表
    setPlayersInfo(playersInfo) {
        this.playersInfo = playersInfo;
    }

    // 获取格子的显示信息
    getCellDisplayInfo(x, y) {
        if (!this.isValidPosition(x, y)) return null;
        
        const cell = this.gameMap[x][y];
        const visible = this.isPositionVisible(x, y);
        
        return {
            type: cell.type,
            belonging: cell.belonging,
            number: cell.number,
            visible: visible,
            color: this.playerColors[cell.belonging] || this.playerColors[0],
            fontLevel: this.getFontSizeLevel(cell.number)
        };
    }
}
