/*
	MIT License http://www.opensource.org/licenses/mit-license.php
*/

"use strict";

/** @typedef {undefined | null | number | string | boolean | Buffer | EXPECTED_OBJECT | (() => ComplexSerializableType[] | Promise<ComplexSerializableType[]>)} ComplexSerializableType */

/** @typedef {undefined | null | number | bigint | string | boolean | Buffer | (() => PrimitiveSerializableType[] | Promise<PrimitiveSerializableType[]>)} PrimitiveSerializableType */

/** @typedef {Buffer | (() => BufferSerializableType[] | Promise<BufferSerializableType[]>)} BufferSerializableType */

module.exports = {};
