/* 导入Quicksand字体 */
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap');

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Quicksand', sans-serif;
    background-color: #383838; /* rgb(56,56,56) */
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 房间主容器 */
.room-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 房间头部 */
.room-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.room-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.room-title {
    font-family: 'Quicksand', sans-serif;
    font-weight: 700;
    font-size: 28px;
    color: #e1f5fe;
}

.room-status {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

/* 主要内容区域 */
.room-content {
    display: flex;
    gap: 30px;
    flex: 1;
    margin-bottom: 20px;
}

/* 面板通用样式 */
.settings-panel,
.players-panel {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    flex: 1;
}

.panel-title {
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    font-size: 20px;
    color: #e1f5fe;
    margin-bottom: 20px;
    text-align: center;
}

/* 设置面板 */
.setting-group {
    margin-bottom: 20px;
}

.setting-label {
    display: block;
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 8px;
}

.setting-select {
    width: 100%;
    padding: 10px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 14px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #ffffff;
    border: 1px dashed #666;
    border-radius: 4px;
    outline: none;
}

.setting-select:focus {
    border-color: #448aff;
    box-shadow: 0 0 4px rgba(68, 138, 255, 0.3);
}

.setting-select option {
    background-color: #383838;
    color: #ffffff;
}

.setting-actions {
    margin-top: 30px;
    text-align: center;
}

/* 玩家面板 */
.team-selection {
    margin-bottom: 20px;
}

.team-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.team-btn {
    padding: 8px 16px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 2px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.team-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.team-btn.selected {
    border-color: #448aff;
    background-color: rgba(68, 138, 255, 0.3);
}

/* 玩家列表 */
.players-list {
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    border-left: 4px solid #666;
}

.player-item.ready {
    border-left-color: #4caf50;
}

.player-item.host {
    border-left-color: #ff9800;
}

.player-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.player-name {
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
}

.player-team {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.player-status {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.2);
}

.player-status.ready {
    background-color: rgba(76, 175, 80, 0.3);
    color: #4caf50;
}

.player-status.host {
    background-color: rgba(255, 152, 0, 0.3);
    color: #ff9800;
}

/* 准备区域 */
.ready-section {
    text-align: center;
}

.ready-status {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 10px;
}

/* 聊天区域 */
.chat-section {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    height: 200px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px dashed #666;
    border-radius: 4px;
    padding: 10px;
    overflow-y: auto;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    line-height: 1.4;
    margin-bottom: 10px;
}

.chat-input-container {
    display: flex;
    gap: 10px;
}

.chat-input {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px dashed #666;
    border-radius: 4px;
    padding: 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    outline: none;
}

.chat-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.chat-input:focus {
    border-color: #448aff;
    box-shadow: 0 0 4px rgba(68, 138, 255, 0.3);
}

/* 按钮样式 */
.btn {
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 14px;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: rgba(68, 138, 255, 0.8);
    color: #ffffff;
}

.btn-primary:hover {
    background-color: rgba(68, 138, 255, 0.9);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.btn-ready {
    background-color: rgba(76, 175, 80, 0.8);
    color: #ffffff;
    padding: 12px 30px;
    font-size: 16px;
}

.btn-ready:hover {
    background-color: rgba(76, 175, 80, 0.9);
}

.btn-ready.not-ready {
    background-color: rgba(244, 67, 54, 0.8);
}

.btn-ready.not-ready:hover {
    background-color: rgba(244, 67, 54, 0.9);
}

.btn-send {
    background-color: rgba(68, 138, 255, 0.8);
    color: #ffffff;
    padding: 8px 16px;
}

.btn-send:hover {
    background-color: rgba(68, 138, 255, 0.9);
}

/* 模态框样式 */
.start-game-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: #242424;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.modal-header {
    margin-bottom: 20px;
}

.modal-icon {
    width: 80px;
    height: 64px;
    object-fit: contain;
}

.modal-body {
    margin-bottom: 30px;
}

.modal-title {
    font-family: 'Quicksand', sans-serif;
    font-weight: 700;
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 15px;
}

.modal-message {
    font-family: 'Quicksand', sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

.modal-footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-btn {
    font-family: 'Quicksand', sans-serif;
    font-weight: 500;
    font-size: 16px;
    background-color: rgba(68, 138, 255, 0.8);
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-btn:hover {
    background-color: rgba(68, 138, 255, 0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .room-content {
        flex-direction: column;
    }
    
    .room-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .team-buttons {
        justify-content: center;
    }
}
