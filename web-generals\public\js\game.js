/**
 * 游戏主控制器 - 完全复制Qt GameWindow的界面和交互
 */
class GameController {
    constructor() {
        this.engine = new GameEngine();
        this.wsClient = window.wsClient;
        this.roomId = this.getRoomIdFromUrl();
        
        // DOM元素
        this.gameContainer = null;
        this.mapArea = null;
        this.mapGrid = null;
        this.mapOverlay = null;
        this.rightPanel = null;
        this.leaderboard = null;
        this.chatArea = null;
        this.chatMessages = null;
        this.chatInput = null;
        this.focusIndicator = null;
        
        // 游戏状态
        this.gameStarted = false;
        this.keyboardEnabled = true;
        
        this.initializeDOM();
        this.bindEvents();
        this.connectToGame();
    }

    // 从URL获取房间ID
    getRoomIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/game\/(\d{4})/);
        return match ? match[1] : null;
    }

    // 初始化DOM元素
    initializeDOM() {
        this.gameContainer = document.getElementById('game-container');
        this.mapArea = document.getElementById('map-area');
        this.mapGrid = document.getElementById('map-grid');
        this.mapOverlay = document.getElementById('map-overlay');
        this.rightPanel = document.getElementById('right-panel');
        this.leaderboard = document.getElementById('leaderboard');
        this.chatArea = document.getElementById('chat-area');
        this.chatMessages = document.getElementById('chat-messages');
        this.chatInput = document.getElementById('chat-input');
        this.focusIndicator = document.getElementById('focus-indicator');
        
        // 设置初始状态
        this.updateLayout();
    }

    // 绑定事件
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        
        // 聊天输入
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.updateLayout());
        
        // WebSocket事件
        this.wsClient.on('qt-message', (data) => this.handleGameMessage(data));
        this.wsClient.on('qt-disconnected', () => this.handleDisconnection());
        
        // 模态框事件
        this.bindModalEvents();
    }

    // 绑定模态框事件
    bindModalEvents() {
        // 投降确认
        document.getElementById('surrender-btn')?.addEventListener('click', () => {
            document.getElementById('surrender-modal').style.display = 'flex';
        });
        
        document.getElementById('confirm-surrender-btn')?.addEventListener('click', () => {
            this.surrender();
            document.getElementById('surrender-modal').style.display = 'none';
        });
        
        document.getElementById('cancel-surrender-btn')?.addEventListener('click', () => {
            document.getElementById('surrender-modal').style.display = 'none';
        });
        
        // 游戏结束
        document.getElementById('watch-btn')?.addEventListener('click', () => {
            this.spectate();
            document.getElementById('end-game-modal').style.display = 'none';
        });
        
        document.getElementById('exit-btn')?.addEventListener('click', () => {
            this.exitGame();
        });
    }

    // 连接到游戏
    connectToGame() {
        if (!this.roomId) {
            console.error('No room ID found');
            this.showError('Invalid room ID');
            return;
        }
        
        // 这里应该从lobby页面传递连接信息
        // 暂时使用localStorage获取
        const nickname = localStorage.getItem('nickname') || 'Anonymous';
        const serverAddress = localStorage.getItem('serverAddress') || 'localhost';
        
        this.wsClient.connectToQtServer(serverAddress, this.roomId, nickname)
            .then(() => {
                console.log('Connected to game server');
                this.addChatMessage('System', 'Connected to game server');
            })
            .catch((error) => {
                console.error('Failed to connect to game server:', error);
                this.showError('Failed to connect to game server');
            });
    }

    // 处理游戏消息
    handleGameMessage(data) {
        const { type, data: msgData } = data;
        
        switch (type) {
            case 'InitGame':
                this.initializeGame(msgData);
                break;
                
            case 'UpdateMap':
                this.updateGameMap(msgData);
                break;
                
            case 'Chat':
                if (Array.isArray(msgData) && msgData.length >= 2) {
                    this.addChatMessage(msgData[0], msgData[1]);
                }
                break;
                
            case 'GameEnd':
                this.handleGameEnd(msgData);
                break;
                
            case 'PlayerInfo':
                this.updatePlayerInfo(msgData);
                break;
                
            default:
                console.log('Unhandled game message:', type, msgData);
        }
    }

    // 初始化游戏
    initializeGame(gameData) {
        console.log('Initializing game with data:', gameData);
        
        // 解析游戏初始化数据
        const gameMode = gameData[gameData.length - 1];
        const mapData = gameData.slice(0, -1);
        
        // 初始化游戏引擎
        this.engine.initializeMap(mapData, gameMode);
        
        // 创建地图UI
        this.createMapUI();
        
        // 聚焦到将军位置
        this.focusGeneral();
        
        // 更新布局
        this.updateLayout();
        
        this.gameStarted = true;
        this.addChatMessage('System', 'Game started!');
    }

    // 创建地图UI
    createMapUI() {
        const { mapWidth, mapHeight, unitSize } = this.engine;
        
        // 设置地图网格
        this.mapGrid.style.gridTemplateColumns = `repeat(${mapWidth}, ${unitSize}px)`;
        this.mapGrid.style.gridTemplateRows = `repeat(${mapHeight}, ${unitSize}px)`;
        this.mapGrid.style.width = `${mapWidth * unitSize + (mapWidth - 1) * 2}px`;
        this.mapGrid.style.height = `${mapHeight * unitSize + (mapHeight - 1) * 2}px`;
        
        // 清空现有内容
        this.mapGrid.innerHTML = '';
        
        // 创建地图格子
        for (let i = 1; i <= mapHeight; i++) {
            for (let j = 1; j <= mapWidth; j++) {
                const cell = this.createMapCell(i, j);
                this.mapGrid.appendChild(cell);
            }
        }
        
        // 更新焦点指示器
        this.updateFocusIndicator();
    }

    // 创建地图格子
    createMapCell(x, y) {
        const cell = document.createElement('div');
        cell.className = 'map-cell';
        cell.dataset.x = x;
        cell.dataset.y = y;
        
        // 添加点击事件
        cell.addEventListener('click', () => this.handleCellClick(x, y));
        
        // 更新格子显示
        this.updateMapCell(cell, x, y);
        
        return cell;
    }

    // 更新地图格子显示
    updateMapCell(cellElement, x, y) {
        const displayInfo = this.engine.getCellDisplayInfo(x, y);
        if (!displayInfo) return;
        
        const { type, belonging, number, visible, color, fontLevel } = displayInfo;
        
        // 清除之前的样式
        cellElement.className = 'map-cell';
        
        // 设置地形类型
        switch (type) {
            case this.engine.CellType.LAND:
                cellElement.classList.add('cell-land');
                break;
            case this.engine.CellType.MOUNTAIN:
                cellElement.classList.add('cell-mountain');
                break;
            case this.engine.CellType.CITY:
                cellElement.classList.add('cell-city');
                break;
            case this.engine.CellType.GENERAL:
                cellElement.classList.add('cell-general');
                break;
            case this.engine.CellType.SWAMP:
                cellElement.classList.add('cell-swamp');
                break;
        }
        
        // 设置玩家颜色
        if (belonging > 0) {
            cellElement.classList.add(`cell-player-${belonging}`);
            cellElement.style.backgroundColor = color;
        }
        
        // 设置数字显示
        if (visible && (type === this.engine.CellType.CITY || type === this.engine.CellType.GENERAL || belonging > 0)) {
            cellElement.textContent = number > 0 ? number.toString() : '';
            
            // 设置字体大小
            const fontSize = this.engine.unitSize * this.engine.fontSizeMap[fontLevel];
            cellElement.style.fontSize = `${fontSize}px`;
        } else {
            cellElement.textContent = '';
        }
        
        // 处理不可见区域
        if (!visible) {
            cellElement.style.opacity = '0.3';
        } else {
            cellElement.style.opacity = '1';
        }
    }

    // 更新整个地图显示
    updateAllMapCells() {
        const cells = this.mapGrid.querySelectorAll('.map-cell');
        cells.forEach(cell => {
            const x = parseInt(cell.dataset.x);
            const y = parseInt(cell.dataset.y);
            this.updateMapCell(cell, x, y);
        });
    }

    // 处理格子点击
    handleCellClick(x, y) {
        if (!this.gameStarted || !this.keyboardEnabled) return;
        
        // 设置焦点到点击的位置
        this.engine.setFocus(x, y);
        this.updateFocusIndicator();
    }

    // 更新焦点指示器
    updateFocusIndicator() {
        const { x, y } = this.engine.focus;
        const { unitSize } = this.engine;
        
        // 计算焦点指示器的位置
        const mapRect = this.mapGrid.getBoundingClientRect();
        const containerRect = this.mapArea.getBoundingClientRect();
        
        const cellX = (y - 1) * (unitSize + 2); // +2 for gap
        const cellY = (x - 1) * (unitSize + 2);
        
        this.focusIndicator.style.width = `${unitSize}px`;
        this.focusIndicator.style.height = `${unitSize}px`;
        this.focusIndicator.style.left = `${mapRect.left - containerRect.left + cellX}px`;
        this.focusIndicator.style.top = `${mapRect.top - containerRect.top + cellY}px`;
    }

    // 更新布局
    updateLayout() {
        if (this.engine.mapWidth > 0) {
            this.engine.calculateMapSize();
            this.createMapUI();
        }
    }

    // 处理键盘按键
    handleKeyPress(event) {
        if (!this.gameStarted || !this.keyboardEnabled) return;

        // 如果聊天输入框获得焦点，不处理游戏按键
        if (document.activeElement === this.chatInput) return;

        switch (event.key) {
            case 'ArrowUp':
            case 'w':
            case 'W':
                event.preventDefault();
                this.moveFocus('up');
                break;

            case 'ArrowDown':
            case 's':
            case 'S':
                event.preventDefault();
                this.moveFocus('down');
                break;

            case 'ArrowLeft':
            case 'a':
            case 'A':
                event.preventDefault();
                this.moveFocus('left');
                break;

            case 'ArrowRight':
            case 'd':
            case 'D':
                event.preventDefault();
                this.moveFocus('right');
                break;

            case ' ':
                event.preventDefault();
                this.selectCurrentCell();
                break;

            case 'Enter':
                event.preventDefault();
                this.chatInput.focus();
                break;

            case 'Escape':
                event.preventDefault();
                this.clearSelection();
                break;

            case 'g':
            case 'G':
                event.preventDefault();
                this.focusGeneral();
                break;
        }
    }

    // 移动焦点
    moveFocus(direction) {
        const newPos = this.engine.moveFocus(direction);
        this.updateFocusIndicator();
    }

    // 选择当前格子
    selectCurrentCell() {
        const { x, y } = this.engine.focus;
        // 这里实现选择逻辑，用于移动军队
        console.log(`Selected cell: ${x}, ${y}`);
    }

    // 清除选择
    clearSelection() {
        // 清除移动选择
        console.log('Cleared selection');
    }

    // 聚焦到将军位置
    focusGeneral() {
        const generalPos = this.engine.focusGeneral();
        if (generalPos) {
            this.updateFocusIndicator();
        }
    }

    // 更新游戏地图
    updateGameMap(mapDiff) {
        this.engine.updateMapDiff(mapDiff);
        this.updateAllMapCells();
        this.updateLeaderboard();
    }

    // 更新排行榜
    updateLeaderboard() {
        const leaderboardData = this.engine.getLeaderboardData();

        // 更新回合信息
        document.getElementById('round-info').textContent = `Round: ${leaderboardData.round}`;

        // 更新排行榜内容
        const boardContent = document.getElementById('board-content');
        boardContent.innerHTML = '';

        // 这里需要根据实际的排行榜数据格式来实现
        // 暂时显示占位内容
        const placeholderRow = document.createElement('div');
        placeholderRow.className = 'board-row';
        placeholderRow.innerHTML = `
            <div class="board-cell">Player 1</div>
            <div class="board-cell">100</div>
            <div class="board-cell">50</div>
        `;
        boardContent.appendChild(placeholderRow);
    }

    // 发送聊天消息
    sendChatMessage() {
        const message = this.chatInput.value.trim();
        if (message) {
            this.wsClient.sendChat(message);
            this.chatInput.value = '';
        }
        this.chatInput.blur();
        this.keyboardEnabled = true;
    }

    // 添加聊天消息
    addChatMessage(sender, message) {
        const messageElement = document.createElement('div');
        messageElement.innerHTML = `<strong>${sender}:</strong> ${message}`;
        this.chatMessages.appendChild(messageElement);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    // 处理游戏结束
    handleGameEnd(endData) {
        this.engine.gameEnded = true;

        const modal = document.getElementById('end-game-modal');
        const title = document.getElementById('modal-title');
        const message = document.getElementById('modal-message');

        // 根据结束数据设置标题和消息
        if (endData && endData.winner) {
            title.textContent = endData.winner === this.engine.playerId ? 'Victory!' : 'Defeat';
            message.textContent = endData.message || 'The game has ended.';
        } else {
            title.textContent = 'Game Over';
            message.textContent = 'The game has ended.';
        }

        modal.style.display = 'flex';
    }

    // 投降
    surrender() {
        this.wsClient.surrender(this.engine.playerId);
        this.engine.surrendered = true;
        this.addChatMessage('System', 'You have surrendered.');
    }

    // 观战
    spectate() {
        this.engine.spectated = true;
        this.addChatMessage('System', 'You are now spectating.');
    }

    // 退出游戏
    exitGame() {
        window.location.href = '/lobby';
    }

    // 处理断开连接
    handleDisconnection() {
        this.addChatMessage('System', 'Disconnected from game server.');
        this.showError('Connection lost');
    }

    // 更新玩家信息
    updatePlayerInfo(playerData) {
        if (Array.isArray(playerData) && playerData.length >= 3) {
            this.engine.setPlayerInfo(playerData[0], playerData[1], playerData[2]);
        }
    }

    // 显示错误
    showError(message) {
        this.addChatMessage('Error', message);
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    window.gameController = new GameController();
});
}
