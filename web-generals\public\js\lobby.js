/**
 * 大厅页面逻辑 - 完全复制Qt StartWindow的行为
 */
class LobbyManager {
    constructor() {
        this.wsClient = window.wsClient;
        this.currentRoom = null;
        this.playerTeam = null;
        this.gameWindow = null;
        
        this.initializeElements();
        this.bindEvents();
        this.connectToWebServer();
    }

    initializeElements() {
        // 获取DOM元素
        this.nicknameInput = document.getElementById('nickname-input');
        this.roomInput = document.getElementById('room-input');
        this.connectBtn = document.getElementById('connect-btn');
        this.readyBtn = document.getElementById('ready-btn');
        this.createBtn = document.getElementById('create-btn');
        this.statusMessage = document.getElementById('status-message');
        this.teamSection = document.getElementById('team-section');
        this.teamButtons = document.getElementById('team-buttons');
        this.roomListSection = document.getElementById('room-list-section');
        this.roomList = document.getElementById('room-list');
    }

    bindEvents() {
        // 按钮事件
        this.connectBtn.addEventListener('click', () => this.onConnectClick());
        this.readyBtn.addEventListener('click', () => this.onReadyClick());
        this.createBtn.addEventListener('click', () => this.onCreateClick());

        // 输入框事件
        this.nicknameInput.addEventListener('input', () => this.validateNickname());
        this.roomInput.addEventListener('input', () => this.validateRoomInput());
        
        // 回车键支持
        this.nicknameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.roomInput.focus();
        });
        this.roomInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.onConnectClick();
        });

        // WebSocket事件
        this.wsClient.on('connected', () => this.onWebServerConnected());
        this.wsClient.on('disconnected', () => this.onWebServerDisconnected());
        this.wsClient.on('room-created', (data) => this.onRoomCreated(data));
        this.wsClient.on('room-joined', (data) => this.onRoomJoined(data));
        this.wsClient.on('room-list', (data) => this.onRoomListReceived(data));
        this.wsClient.on('error', (data) => this.onError(data));

        // Qt服务器事件
        this.wsClient.on('qt-message', (data) => this.onQtMessage(data));
        this.wsClient.on('qt-disconnected', () => this.onQtDisconnected());
    }

    connectToWebServer() {
        this.wsClient.connect();
        this.updateStatus('[Connecting to web server...]');
    }

    // 验证昵称（复制Qt逻辑）
    validateNickname() {
        const nickname = this.nicknameInput.value;
        const nicknameLen = new TextEncoder().encode(nickname).length;
        
        if (nickname.length === 0) return true;
        
        if (!(3 <= nicknameLen && nicknameLen <= 15)) {
            this.updateStatus('[Disconnected]\nError: Illegal nickname length');
            return false;
        }
        
        // 检查前后空格
        if (nickname.startsWith(' ') || nickname.endsWith(' ')) {
            this.updateStatus('[Disconnected]\nError: Invisible prefixes and suffixes are prohibited');
            return false;
        }
        
        return true;
    }

    // 验证房间号输入
    validateRoomInput() {
        const roomId = this.roomInput.value;
        // 只允许4位数字
        if (roomId && !/^\d{0,4}$/.test(roomId)) {
            this.roomInput.value = roomId.replace(/\D/g, '').slice(0, 4);
        }
    }

    // 连接按钮点击
    async onConnectClick() {
        if (this.wsClient.status === 'disconnected') {
            // 验证输入
            if (!this.validateNickname()) return;

            const nickname = this.nicknameInput.value || 'Anonymous';
            const roomId = this.roomInput.value;

            if (!roomId || roomId.length !== 4) {
                this.updateStatus('Error: Please enter a 4-digit room number');
                return;
            }

            try {
                this.connectBtn.textContent = 'Cancel';
                this.connectBtn.disabled = false;
                this.updateStatus(`[Connecting]\nRoom: ${roomId}`);

                // 保存连接信息到localStorage，供游戏页面使用
                localStorage.setItem('nickname', nickname);
                localStorage.setItem('roomId', roomId);
                localStorage.setItem('serverAddress', 'localhost'); // 暂时使用localhost

                // 直接跳转到游戏页面
                window.location.href = `/game/${roomId}`;

            } catch (error) {
                this.updateStatus(`Error: ${error.message}`);
                this.connectBtn.textContent = 'Connect';
            }
        } else {
            // 断开连接
            this.wsClient.disconnect();
        }
    }

    // 连接成功
    onConnected(nickname, roomId) {
        this.connectBtn.textContent = 'Disconnect';
        this.readyBtn.disabled = false;
        this.nicknameInput.disabled = true;
        this.roomInput.disabled = true;
        
        this.updateStatus(`[Connected]\nRoom: ${roomId}\nNickname: ${nickname}`);
        this.teamSection.style.display = 'block';
        
        // 创建游戏窗口（但不显示）
        this.createGameWindow();
    }

    // 准备按钮点击
    onReadyClick() {
        this.wsClient.ready();
        this.readyBtn.disabled = true;
        this.connectBtn.focus();
    }

    // 创建房间按钮点击
    onCreateClick() {
        if (!this.validateNickname()) return;
        
        const nickname = this.nicknameInput.value || 'Anonymous';
        this.wsClient.createRoom({
            gameMode: 0,
            gameSpeed: 1.0,
            maxPlayers: 8
        });
    }

    // Web服务器连接成功
    onWebServerConnected() {
        this.updateStatus('[Disconnected]');
        this.loadRoomList();
    }

    // Web服务器断开连接
    onWebServerDisconnected() {
        this.updateStatus('[Disconnected]\nWeb server connection lost');
    }

    // 房间创建成功
    onRoomCreated(data) {
        const roomId = data.roomId;
        this.roomInput.value = roomId;
        this.updateStatus(`[Room Created]\nRoom ID: ${roomId}\nClick Connect to join`);
    }

    // 加入房间成功
    onRoomJoined(data) {
        this.currentRoom = data.room;
        this.updateStatus(`[Joined Room]\nRoom: ${data.room.id}\nPlayers: ${data.room.players.length}/${data.room.settings.maxPlayers}`);
    }

    // 收到房间列表
    onRoomListReceived(data) {
        this.renderRoomList(data.rooms);
    }

    // 错误处理
    onError(data) {
        this.updateStatus(`Error: ${data.message}`);
    }

    // 处理Qt服务器消息
    onQtMessage(data) {
        const { type, data: msgData } = data;
        
        switch (type) {
            case 'Status':
                if (Array.isArray(msgData) && msgData.length > 0) {
                    if (msgData[0].includes('Conflicting nickname')) {
                        this.updateStatus('[Disconnected]\nError: Conflicting nickname');
                        this.wsClient.disconnect();
                    } else if (msgData[0].includes('spectator')) {
                        this.updateStatus('[Connected as Spectator]\n' + msgData[0]);
                    } else {
                        this.updateStatus('[Connected]\n' + msgData[0]);
                        this.updateTeamButtons(msgData.slice(1));
                    }
                }
                break;
                
            case 'InitGame':
                // 游戏开始，跳转到游戏页面
                this.startGame(msgData);
                break;
                
            case 'UpdateMap':
                // 游戏更新，如果游戏窗口存在则更新
                if (this.gameWindow) {
                    this.gameWindow.updateMap(msgData);
                }
                break;
        }
    }

    // Qt服务器断开连接
    onQtDisconnected() {
        this.connectBtn.textContent = 'Connect';
        this.readyBtn.disabled = true;
        this.nicknameInput.disabled = false;
        this.roomInput.disabled = false;
        this.teamSection.style.display = 'none';
        this.updateStatus('[Disconnected]\nConnection to game server lost');
    }

    // 更新状态消息
    updateStatus(message) {
        this.statusMessage.textContent = message;
    }

    // 更新团队按钮
    updateTeamButtons(teamsData) {
        this.teamButtons.innerHTML = '';
        
        teamsData.forEach((teamPlayers, index) => {
            if (Array.isArray(teamPlayers) && teamPlayers.length > 0) {
                const button = document.createElement('button');
                button.className = 'team-button';
                button.textContent = `Team ${index + 1}\n${teamPlayers.join(', ')}`;
                button.addEventListener('click', () => this.selectTeam(index + 1));
                this.teamButtons.appendChild(button);
            }
        });
        
        // 添加"创建新团队"按钮
        const newTeamButton = document.createElement('button');
        newTeamButton.className = 'team-button';
        newTeamButton.textContent = 'Create and Join\nNew Team';
        newTeamButton.addEventListener('click', () => this.selectTeam(8)); // 假设最大团队数为8
        this.teamButtons.appendChild(newTeamButton);
    }

    // 选择团队
    selectTeam(teamId) {
        this.playerTeam = teamId;
        this.wsClient.chooseTeam(teamId);
        
        // 更新按钮状态
        document.querySelectorAll('.team-button').forEach((btn, index) => {
            btn.classList.toggle('selected', index === teamId - 1);
        });
    }

    // 加载房间列表
    loadRoomList() {
        this.wsClient.getRoomList();
    }

    // 渲染房间列表
    renderRoomList(rooms) {
        this.roomList.innerHTML = '';
        
        rooms.forEach(room => {
            const roomItem = document.createElement('div');
            roomItem.className = 'room-item';
            roomItem.addEventListener('click', () => this.joinRoomFromList(room.id));
            
            roomItem.innerHTML = `
                <div class="room-info">
                    <div class="room-id">Room ${room.id}</div>
                    <div class="room-details">${room.playerCount}/${room.maxPlayers} players</div>
                </div>
                <div class="room-status ${room.status}">${room.status}</div>
            `;
            
            this.roomList.appendChild(roomItem);
        });
    }

    // 从列表加入房间
    joinRoomFromList(roomId) {
        this.roomInput.value = roomId;
        this.onConnectClick();
    }

    // 创建游戏窗口
    createGameWindow() {
        // 这里将在后续实现游戏窗口
        console.log('Game window would be created here');
    }

    // 开始游戏
    startGame(gameData) {
        // 跳转到游戏页面
        const roomId = this.wsClient.roomId;
        window.location.href = `/game/${roomId}`;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.lobbyManager = new LobbyManager();
});
