<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generals.OI - Room</title>
    <link rel="stylesheet" href="css/room.css">
    <link rel="icon" href="img/Icon-Blue.png" type="image/png">
</head>
<body>
    <!-- 房间主容器 -->
    <div class="room-container" id="room-container">
        <!-- 房间信息区域 -->
        <div class="room-header">
            <div class="room-info">
                <h2 class="room-title">Room <span id="room-id">0000</span></h2>
                <div class="room-status" id="room-status">Waiting for players...</div>
            </div>
            <div class="room-actions">
                <button class="btn btn-secondary" id="leave-btn">Leave Room</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="room-content">
            <!-- 左侧：游戏设置 -->
            <div class="settings-panel">
                <h3 class="panel-title">Game Settings</h3>
                
                <div class="setting-group">
                    <label class="setting-label">Game Mode:</label>
                    <select class="setting-select" id="game-mode">
                        <option value="0">Classic</option>
                        <option value="1">Crystal Clear</option>
                        <option value="2">Misty Veil</option>
                        <option value="4">Silent War</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label class="setting-label">Game Speed:</label>
                    <select class="setting-select" id="game-speed">
                        <option value="0.5">0.5x</option>
                        <option value="1.0" selected>1.0x</option>
                        <option value="2.0">2.0x</option>
                        <option value="4.0">4.0x</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label class="setting-label">Max Players:</label>
                    <select class="setting-select" id="max-players">
                        <option value="2">2 Players</option>
                        <option value="4">4 Players</option>
                        <option value="6">6 Players</option>
                        <option value="8" selected>8 Players</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label class="setting-label">Map:</label>
                    <select class="setting-select" id="map-select">
                        <option value="random" selected>Random</option>
                        <option value="small">Small Map</option>
                        <option value="medium">Medium Map</option>
                        <option value="large">Large Map</option>
                    </select>
                </div>

                <div class="setting-actions">
                    <button class="btn btn-primary" id="apply-settings-btn">Apply Settings</button>
                </div>
            </div>

            <!-- 右侧：玩家列表和队伍 -->
            <div class="players-panel">
                <h3 class="panel-title">Players & Teams</h3>
                
                <!-- 队伍选择 -->
                <div class="team-selection">
                    <div class="team-buttons" id="team-buttons">
                        <!-- 动态生成队伍按钮 -->
                    </div>
                </div>

                <!-- 玩家列表 -->
                <div class="players-list" id="players-list">
                    <!-- 动态生成玩家列表 -->
                </div>

                <!-- 准备状态 -->
                <div class="ready-section">
                    <button class="btn btn-ready" id="ready-btn">Ready</button>
                    <div class="ready-status" id="ready-status">
                        <span id="ready-count">0</span> / <span id="total-count">0</span> players ready
                    </div>
                </div>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-section">
            <div class="chat-messages" id="chat-messages">
                <!-- 聊天消息 -->
            </div>
            <div class="chat-input-container">
                <input type="text" id="chat-input" class="chat-input" placeholder="Type message..." maxlength="100">
                <button class="btn btn-send" id="send-btn">Send</button>
            </div>
        </div>
    </div>

    <!-- 开始游戏确认弹窗 -->
    <div class="start-game-modal" id="start-game-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <img src="img/General-Blue.png" alt="General" class="modal-icon">
            </div>
            <div class="modal-body">
                <h3 class="modal-title">Start Game</h3>
                <p class="modal-message">All players are ready. Start the game now?</p>
            </div>
            <div class="modal-footer">
                <button class="modal-btn" id="start-game-btn">Start Game</button>
                <button class="modal-btn" id="cancel-start-btn">Cancel</button>
            </div>
        </div>
    </div>

    <script src="js/websocket-client.js"></script>
    <script src="js/room.js"></script>
</body>
</html>
